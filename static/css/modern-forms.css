/* 现代化表单样式 */
.layui-form {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 32px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.layui-form-item {
    margin-bottom: 24px;
    position: relative;
}

.layui-form-label {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    font-size: 14px;
    padding: 12px 0;
    width: 100px;
    text-align: left;
    position: relative;
}

.layui-form-label::after {
    content: '';
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 2px;
    height: 16px;
    background: linear-gradient(180deg, #3b82f6, #8b5cf6);
    border-radius: 1px;
}

.layui-input, .layui-select {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: #ffffff;
    padding: 14px 16px;
    font-size: 14px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.layui-input:focus, .layui-select:focus {
    outline: none;
    border-color: #3b82f6;
    background: rgba(255, 255, 255, 0.12);
    box-shadow: 
        0 0 0 3px rgba(59, 130, 246, 0.2),
        inset 0 1px 2px rgba(0, 0, 0, 0.1),
        0 4px 12px rgba(59, 130, 246, 0.15);
    transform: translateY(-1px);
}

.layui-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
    transition: color 0.3s ease;
}

.layui-input:focus::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

/* 现代化按钮样式 */
.layui-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    border: none;
    border-radius: 12px;
    color: #ffffff;
    font-weight: 600;
    font-size: 14px;
    padding: 14px 28px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 
        0 4px 12px rgba(59, 130, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.layui-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.layui-btn:hover::before {
    left: 100%;
}

.layui-btn:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 8px 20px rgba(59, 130, 246, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
}

.layui-btn:active {
    transform: translateY(0);
    box-shadow: 
        0 2px 8px rgba(59, 130, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.layui-btn-primary {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.9);
    box-shadow: 
        0 2px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.layui-btn-primary:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    color: #ffffff;
    box-shadow: 
        0 4px 12px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 选择框样式优化 */
.layui-form-select {
    position: relative;
}

.layui-form-select .layui-select-title {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: #ffffff;
    padding: 14px 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.layui-form-select .layui-select-title:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.3);
}

.layui-form-select .layui-select-title input {
    color: #ffffff;
    background: transparent;
    border: none;
}

.layui-form-select .layui-select-title input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

/* 下拉选项样式 */
.layui-form-select dl {
    background: rgba(30, 41, 59, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    margin-top: 4px;
}

.layui-form-select dl dd {
    color: rgba(255, 255, 255, 0.8);
    padding: 12px 16px;
    transition: all 0.2s ease;
    border-radius: 8px;
    margin: 4px;
}

.layui-form-select dl dd:hover {
    background: rgba(59, 130, 246, 0.2);
    color: #ffffff;
}

.layui-form-select dl dd.layui-this {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    color: #ffffff;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .layui-form {
        padding: 20px;
        margin: 10px;
    }
    
    .layui-form-label {
        width: 80px;
        font-size: 13px;
    }
    
    .layui-input, .layui-select {
        padding: 12px 14px;
        font-size: 13px;
    }
    
    .layui-btn {
        padding: 12px 24px;
        font-size: 13px;
    }
}

/* 表单验证状态 */
.layui-form-danger {
    border-color: #ef4444 !important;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2) !important;
}

.layui-form-danger:focus {
    box-shadow: 
        0 0 0 3px rgba(239, 68, 68, 0.3),
        0 4px 12px rgba(239, 68, 68, 0.2) !important;
}

/* 成功状态 */
.layui-form-success {
    border-color: #10b981 !important;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2) !important;
}

/* 加载状态 */
.layui-btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.layui-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}
