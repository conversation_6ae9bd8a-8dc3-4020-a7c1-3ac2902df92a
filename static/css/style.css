/* 全局样式 */
body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    transition: margin-left 0.3s;
    color: #2c3e50;
    line-height: 1.6;
}

/* 侧边栏样式 */
.sidebar {
    width: 250px;
    background-color: #2c3e50;
    color: #fff;
    position: fixed;
    height: 100vh;
    padding: 20px;
    box-sizing: border-box;
    transition: width 0.3s;
    overflow: visible;
}

.sidebar.collapsed {
    width: 60px;
    padding: 20px 5px;
}

.sidebar h2 {
    text-align: center;
    margin-bottom: 20px;
    white-space: nowrap;
    overflow: hidden;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.sidebar h2 img {
    height: 40px;
    width: auto;
}

.sidebar.collapsed h2 {
    display: none;
}

/* 新增收起时的logo样式 */
.sidebar.collapsed::before {
    content: '';
    display: block;
    width: 40px;
    height: 40px;
    background: url('../img/logo-collapsed.png') center/contain no-repeat;
    margin: 0 auto 20px;
}

.sidebar ul {
    list-style: none;
    padding: 0;
}

.sidebar ul li {
    margin: 15px 0;
}

.sidebar ul li a {
    color: #fff;
    text-decoration: none;
    display: flex;
    align-items: center;
    padding: 10px;
    border-radius: 4px;
    transition: background-color 0.3s;
    white-space: nowrap;
    overflow: hidden;
}

.sidebar ul li a:hover {
    background-color: #34495e;
}

.sidebar ul li a.active {
    background-color: #1abc9c;
}

.sidebar ul li a i {
    margin-right: 10px;
    font-size: 18px;
}

.sidebar.collapsed ul li a i {
    margin-right: 0;
    margin-left: 3px;
    font-size: 20px;
}

.sidebar.collapsed ul li a span {
    display: none;
}

/* 顶部导航栏样式 */
.topbar {
    margin-left: 250px;
    background-color: #fff;
    padding: 15px 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: margin-left 0.3s;
}

.topbar.collapsed {
    margin-left: 60px;
}

.topbar .menu-toggle {
    font-size: 20px;
    cursor: pointer;
}

.topbar .search-container {
    position: relative;
    width: 250px;
}

.topbar .search-box {
    padding-right: 30px;
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.topbar .clear-search {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    font-size: 18px;
    padding: 0 5px;
    display: none;
}

.topbar .clear-search:hover {
    color: #666;
}

.topbar .user-info {
    display: flex;
    align-items: center;
}

.topbar .user-info img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 10px;
}

/* 内容区域样式 */
.content {
    margin-left: 250px;
    padding: 20px;
    transition: margin-left 0.3s;
}

.content.collapsed {
    margin-left: 60px;
}

.content h1 {
    margin-top: 0;
}

.card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    padding: 24px;
    border-radius: 16px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -250px;
        top: 0;
        width: 250px;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease;
        background: #fff;
        box-shadow: none;
    }

    .sidebar.expanded {
        left: 0;
        box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
    }

    .content {
        margin-left: 0 !important;
        width: 100%;
    }

    .topbar {
        margin-left: 0 !important;
        width: 100%;
    }

    .sidebar-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .sidebar-overlay.active {
        display: block;
        opacity: 1;
    }

    .sidebar h2 {
        display: none;
    }

    .sidebar ul li a span {
        display: none;
    }

    .sidebar ul li a i {
        margin-right: 0;
        margin-left: 3px;
        font-size: 20px;
    }

    .search-container {
        width: 100%;
        max-width: 200px;
    }

    .table-container {
        margin: 0 -10px;
        padding: 0 10px;
        overflow-x: auto;
    }

    .data-table {
        font-size: 13px;
    }

    .data-table th,
    .data-table td {
        padding: 8px 10px;
    }

    .btn {
        padding: 4px 8px;
        font-size: 12px;
    }

    .switch {
        width: 40px;
        height: 24px;
    }

    .slider:before {
        height: 18px;
        width: 18px;
    }

    .filter-bar.tabs button {
        padding: 8px 15px;
        font-size: 13px;
    }

    .action-bar {
        flex-wrap: wrap;
    }

    .multi-select-btn,
    .batch-delete-btn {
        padding: 6px 12px;
        font-size: 13px;
    }

    .search-tip {
        font-size: 12px;
        padding: 6px 10px;
    }

    .toast {
        width: 90%;
        max-width: 300px;
        font-size: 13px;
        padding: 10px 15px;
    }

    /* 调整侧边栏选中状态的背景位置 */
    .sidebar ul li a.active {
        background-color: #1abc9c;
        margin-left: -3px; /* 向左偏移以覆盖白边 */
        padding-left: 23px; /* 补偿左偏移，保持内容对齐 */
        width: calc(100% + 3px); /* 确保宽度补偿偏移量 */
    }

    /* 调整数据表格在移动端的显示 */
    .data-table {
        min-width: 600px; /* 确保表格有最小宽度 */
    }

    /* 确保所有列都显示 */
    .data-table th,
    .data-table td {
        display: table-cell !important; /* 强制显示所有列 */
        padding: 8px 10px;
        white-space: nowrap;
        font-size: 13px;
    }

    /* 调整提交时间列的宽度 */
    .data-table th:nth-child(4),
    .data-table td:nth-child(4) { /* 提交时间列 */
        min-width: 120px;
        text-align: center;
    }

    /* 确保表格容器可以水平滚动 */
    .table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
    .data-table {
        font-size: 12px;
    }

    .data-table th,
    .data-table td {
        padding: 6px 8px;
    }

    .data-table td:nth-child(4),
    .data-table th:nth-child(4) {
        display: none;
    }

    .btn {
        padding: 3px 6px;
        font-size: 11px;
    }

    .search-container {
        max-width: 150px;
    }
}

/* 添加触摸滚动支持 */
.table-container {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
}

/* 优化表格内容在移动端的显示 */
@media (max-width: 768px) {
    .data-table td {
        max-width: 120px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .data-table td[data-fulltext]:active {
        position: relative;
        overflow: visible;
        white-space: normal;
        z-index: 1;
        background: white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        padding: 8px;
        border-radius: 4px;
        max-width: none;
        width: auto;
    }
}

/* 表格容器 */
.table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch; /* 移动端流畅滚动 */
    background: linear-gradient(to right, white 30%, rgba(255, 255, 255, 0)),
    linear-gradient(to left, white 30%, rgba(255, 255, 255, 0)) 100% 0,
    radial-gradient(farthest-side at 0% 50%, rgba(0, 0, 0, .2), transparent),
    radial-gradient(farthest-side at 100% 50%, rgba(0, 0, 0, .2), transparent);
    background-repeat: no-repeat;
    background-size: 40px 100%, 40px 100%, 14px 100%, 14px 100%;
    background-attachment: local, local, scroll, scroll;
}

/* 数据表格样式 */
.data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    min-width: 800px; /* 最小保证基本布局 */
}

.data-table th {
    background-color: #f8f9fa;
    padding: 15px 20px;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
    position: relative;
}

.data-table th i {
    margin-right: 8px;
    font-size: 16px;
    vertical-align: middle;
}

.data-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #dee2e6;
    max-width: 200px; /* 最大宽度限制 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.data-table tr:hover {
    background-color: #f8f9fa;
}

/* 状态标签 */
.status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.9em;
}

.status.active {
    background-color: #d4edda;
    color: #155724;
}

/* 操作按钮 */
.btn {
    border: none;
    padding: 6px 10px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    margin: 0 2px;
}

.edit-btn {
    background-color: #ffc107;
    color: #212529;
}

.delete-btn {
    background-color: #dc3545;
    color: white;
}

/* 响应式表格 */
@media (max-width: 768px) {
    .data-table {
        min-width: 600px; /* 保证基本可读性 */
    }

    .data-table td {
        max-width: 120px !important; /* 强制覆盖其他样式 */
        font-size: 0.85em; /* 调小字号 */
    }
}

/* 添加分组相关样式 */
.filter-bar {
    margin: 15px 0;
    display: flex;
    gap: 10px;
}

.group-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
}

.group-tag {
    display: inline-block;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.85em;
    font-weight: 500;
}

.group-tag.admin {
    background-color: #d1e8ff;
    color: #004085;
}

.group-tag.vip {
    background-color: #fff3cd;
    color: #856404;
}

.group-tag.normal {
    background-color: #e2e3e5;
    color: #383d41;
}

/* 添加模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
    background-color: #fff;
    margin: 15% auto;
    padding: 20px;
    width: 400px;
    border-radius: 8px;
    position: relative;
}

.close {
    position: absolute;
    right: 20px;
    top: 10px;
    font-size: 24px;
    cursor: pointer;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.save-btn {
    background-color: #28a745 !important;
    width: 100%;
    padding: 10px !important;
}

/* 添加激活状态的按钮样式 */
.filter-bar button.active {
    background-color: #1abc9c;
    color: white;
}

/* 改进选项卡样式（替换原有.group-filter样式） */
.filter-bar.tabs {
    border-bottom: 2px solid #dee2e6;
    margin-bottom: 20px;
    gap: 0;
}

.filter-bar.tabs button {
    background: none;
    border: none;
    padding: 12px 30px;
    cursor: pointer;
    color: #6c757d;
    position: relative;
    transition: all 0.3s;
    font-size: 15px;
    border-radius: 4px 4px 0 0;
}

.filter-bar.tabs button:hover {
    background-color: #f8f9fa;
}

.filter-bar.tabs button.active {
    color: #1abc9c;
    font-weight: 600;
    background-color: #f8f9fa;
}

.filter-bar.tabs button.active::after {
    content: '';
    position: absolute;
    bottom: -1px; /* 从-2px调整为-1px */
    left: 0;
    width: 100%;
    height: 3px; /* 从2px调整为3px */
    background: #1abc9c;
}

/* 表格对齐优化 */
.data-table th,
.data-table td {
    vertical-align: middle;
}

/* 通用居中列 */
.data-table th:nth-child(3),
.data-table td:nth-child(3), /* 留餐人数列 */
.data-table th:nth-child(2),
.data-table td:nth-child(2), /* 用车人数列 */
.data-table th:last-child,
.data-table td:last-child { /* 操作列 */
    text-align: center;
    width: 15%;
}

/* 针对用车报备表格的特殊列 */
#orders-page .vehicle-table th:nth-child(5),
#orders-page .vehicle-table td:nth-child(5), /* 用车时间 */
#orders-page .vehicle-table th:nth-child(6),
#orders-page .vehicle-table td:nth-child(6) { /* 提交时间 */
    text-align: center;
    width: 15%;
}

/* 新增开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 48px; /* 从40px调整为48px */
    height: 28px; /* 从24px调整为28px */
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 20px; /* 从16px调整为20px */
    width: 20px; /* 从16px调整为20px */
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #1abc9c;
}

input:checked + .slider:before {
    transform: translateX(16px);
}

/* 调整操作列宽度 */
.data-table th:last-child,
.data-table td:last-child {
    width: 140px; /* 从120px调整为140px */
    text-align: center;
}

/* 添加通知样式 */
.toast {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 24px;
    border-radius: 4px;
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        bottom: -50px;
    }
    to {
        bottom: 20px;
    }
}

/* 添加加载状态样式 */
.table-container.loading {
    position: relative;
    min-height: 200px;
}

.table-container.loading::after {
    content: "加载中...";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #666;
    font-size: 16px;
    z-index: 1; /* 添加层级设置 */
}

.table-container.loading table {
    opacity: 0.5;
    pointer-events: none;
}

/* 特定列的特殊处理 */
.data-table td:nth-child(2), /* OPENID列 */
.data-table td:nth-child(3) { /* UID列 */
    max-width: 150px;
    font-family: monospace; /* 等宽字体便于阅读 */
}

/* 添加悬停显示完整内容 */
.data-table td:hover {
    z-index: 2; /* 从1调整为2避免被模态框覆盖 */
    overflow: visible;
    white-space: normal;
    word-break: break-all;
    position: relative;
    background: white;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2); /* 增强投影效果 */
}

/* 新增数据管理表格特定样式 */
#data-page .table-container {
    margin-top: 20px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#data-page table {
    width: 100%;
    border-collapse: collapse;
}

/* 统一表格头部样式 */
#data-page th {
    background-color: #f8f9fa;
    padding: 12px 15px;
    text-align: left;
    border-bottom: 2px solid #dee2e6;
}

/* 统一数据单元格样式 */
#data-page td {
    padding: 12px 15px;
    border-bottom: 1px solid #dee2e6;
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 特定列对齐方式 */
#data-page th:nth-child(2),
#data-page td:nth-child(2) { /* 留餐人数列 */
    text-align: center;
    width: 15%;
}

#data-page th:nth-child(3),
#data-page td:nth-child(3), /* 留餐时间列 */
#data-page th:nth-child(4),
#data-page td:nth-child(4) { /* 提交时间列 */
    text-align: center;
    width: 20%;
}

/* 顶部搜索框容器 */
.search-container {
    position: relative;
    width: 260px;
    margin: 0 10px;
}

/* 搜索框样式 */
.search-box {
    width: 100%;
    height: 20px;
    padding: 4px 30px 4px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 14px;
    font-size: 13px;
    color: #333;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
}

.search-box:focus {
    background-color: #fff;
    border-color: #1abc9c;
    box-shadow: 0 1px 4px rgba(26, 188, 156, 0.1);
    outline: none;
}

/* 清除按钮样式 */
.clear-search {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #999;
    font-size: 12px;
    cursor: pointer;
    padding: 2px;
    display: none;
    transition: color 0.2s ease;
    z-index: 2;
}

.clear-search:hover {
    color: #666;
}

/* 搜索提示样式 */
.search-tip {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    margin-top: 8px;
    padding: 8px 12px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    font-size: 13px;
    color: #666;
    display: none;
    z-index: 1000;
}

.search-tip .highlight {
    color: #1abc9c;
    font-weight: 500;
}

/* 搜索框占位符颜色 */
.search-box::placeholder {
    color: #aaa;
}

/* 搜索框聚焦时的占位符颜色 */
.search-box:focus::placeholder {
    color: #bbb;
}

/* 禁用搜索框内部的清除按钮（Chrome） */
.search-box::-webkit-search-cancel-button {
    display: none;
}

/* 搜索框hover效果 */
.search-box:hover {
    background-color: #fff;
    border-color: #d0d0d0;
}

/* 移动端适配 */
@media (max-width: 768px) {
    #data-page table {
        min-width: 600px;
    }

    #data-page td {
        max-width: 120px !important;
        font-size: 0.85em;
    }

    #data-page .global-search {
        width: 100%;
        padding-right: 40px;
    }

    #data-page th {
        font-size: 13px;
        padding: 12px 15px;
    }

    #data-page td {
        font-size: 13px;
        padding: 12px 15px;
    }

    #data-page .btn {
        padding: 5px 10px;
        margin: 2px;
    }
}

/* 添加数据表格悬停效果 */
#data-page tr:hover {
    background-color: #f8f9fa;
}

#data-page td:hover {
    z-index: 2;
    overflow: visible;
    white-space: normal;
    word-break: break-all;
    position: relative;
    background: white;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
}

/* 新增数据管理选项卡样式 */
#data-page .tabs {
    margin-bottom: 20px;
}

#data-page .tabs button {
    padding: 12px 25px;
    font-size: 14px;
}

/* 调整表格容器高度 */
#data-page .table-container {
    max-height: 600px;
    overflow-y: auto;
}

/* 统一操作列样式 */
#data-page td:last-child {
    width: 120px;
    text-align: center;
}

/* 优化留餐表头对齐 */
#orders-page .meal-table th:nth-child(1),
#orders-page .meal-table td:nth-child(1) { /* 申请人列 */
    width: 20%;
}

#orders-page .meal-table th:nth-child(2),
#orders-page .meal-table td:nth-child(2) { /* 留餐时间 */
    width: 25%;
    text-align: center;
}

#orders-page .meal-table th:nth-child(3),
#orders-page .meal-table td:nth-child(3) { /* 留餐人数 */
    width: 15%;
    text-align: center;
}

#orders-page .meal-table th:nth-child(4),
#orders-page .meal-table td:nth-child(4) { /* 提交时间 */
    width: 25%;
    text-align: center;
}

/* 统一图标+文字排列 */
.data-table th > .header-content {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* 统一表格列宽设置 */
#data-page th:nth-child(1), /* 申请人列 */
#data-page td:nth-child(1) {
    width: 25%;
    min-width: 180px;
    text-align: left;
}

#data-page th:nth-child(2), /* 留餐人数/用车人数 */
#data-page td:nth-child(2) {
    width: 12%;
    min-width: 100px;
    text-align: center;
}

#data-page th:nth-child(3), /* 留餐时间/用车地点 */
#data-page td:nth-child(3) {
    width: 28%;
    min-width: 200px;
    text-align: left;
}

#data-page th:nth-child(4), /* 提交时间/用车事由 */
#data-page td:nth-child(4) {
    width: 25%;
    min-width: 180px;
    text-align: left;
}

#data-page th:nth-child(5), /* 操作列 */
#data-page td:nth-child(5) {
    width: 10%;
    min-width: 120px;
    text-align: center;
}

/* 针对用车表格的特殊列 */
#data-page .vehicle-table th:nth-child(5), /* 用车时间 */
#data-page .vehicle-table td:nth-child(5) {
    width: 20%;
    min-width: 160px;
}

#data-page .vehicle-table th:nth-child(6), /* 提交时间 */
#data-page .vehicle-table td:nth-child(6) {
    width: 15%;
    min-width: 140px;
}

/* 添加列间隔线 */
#data-page th:not(:last-child),
#data-page td:not(:last-child) {
    border-right: 1px solid #f0f0f0;
}

/* 优化表头文字换行 */
#data-page th .header-content {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 强制表格布局 */
#data-page table {
    table-layout: fixed;
    width: 100%;
}

/* 统一表格内容居中 */
#data-page th,
#data-page td {
    text-align: center;
    vertical-align: middle;
}

/* 调整特定列的对齐方式 */
#data-page th:nth-child(1),
#data-page td:nth-child(1), /* 申请人列 */
#data-page th:nth-child(3),
#data-page td:nth-child(3), /* 留餐时间/用车地点 */
#data-page th:nth-child(4),
#data-page td:nth-child(4) { /* 提交时间/用车事由 */
    text-align: center; /* 保持居中但增加内边距 */
    padding-left: 20px;
    padding-right: 20px;
}

/* 调整操作按钮容器 */
#data-page td:last-child {
    display: flex;
    justify-content: center;
    gap: 8px;
}

/* 移动端优化 */
@media (max-width: 768px) {
    #data-page th,
    #data-page td {
        padding-left: 12px;
        padding-right: 12px;
    }

    #data-page td:last-child {
        flex-wrap: wrap;
    }
}

.chart-loading, .chart-error {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 400px;
    font-size: 16px;
}

.chart-loading {
    color: #666;
    animation: pulse 1.5s infinite;
}

.chart-error {
    color: #e74c3c;
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.6;
    }
    50% {
        opacity: 1;
    }
}

/* 移除特殊字体设置 */
#data-page .vehicle-table td:nth-child(4) {
    font-family: inherit; /* 继承表格整体字体 */
}

/* 统一所有数据表格字体 */
#data-page table {
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
}

/* 确保表头样式统一 */
#data-page th {
    font-weight: 600;
    font-size: 15px;
}

/* 统一表格内容样式 */
#data-page td {
    font-size: 14px;
    color: #333;
}

/* 统一所有数据单元格样式 */
#data-page td {
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 14px;
    font-weight: 400; /* 常规字体粗细 */
    color: #333;
    line-height: 1.5;
}

/* 移除所有特殊列样式 */
#data-page .vehicle-table td:nth-child(4),
#data-page .meal-table td:nth-child(3) {
    font-family: inherit;
    font-weight: inherit;
    color: inherit;
}

/* 保留功能性样式 */
#data-page td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
    position: relative;
}

/* 表头保持原有样式 */
#data-page th {
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-weight: 600;
    font-size: 15px;
    color: #2c3e50;
}

/* 移除搜索图标相关样式 */
.search-container::after {
    display: none; /* 隐藏搜索图标 */
}

/* 操作栏样式 */
.action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0;
    padding: 0 10px;
}

.action-bar .left-actions,
.action-bar .right-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.action-bar .btn {
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.multi-select-btn {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #495057;
    padding: 8px 15px;
    border-radius: 4px;
}

.multi-select-btn.active {
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.batch-delete-btn {
    background-color: #dc3545;
    color: white;
    padding: 8px 15px;
    border-radius: 4px;
    border: none;
}

.select-column {
    width: 40px !important;
    min-width: 40px !important;
    padding: 8px !important;
    text-align: center !important;
    vertical-align: middle !important;
}

.select-column input[type="checkbox"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
    margin: 0;
}

/* 确保其他列宽度不变 */
.data-table th:not(.select-column),
.data-table td:not(.select-column) {
    width: auto !important;
}

/* 删除中的行样式 */
.data-table tr.deleting {
    transition: all 0.3s ease;
}

/* 批量删除按钮加载状态 */
.batch-delete-btn:disabled {
    cursor: not-allowed;
}

.batch-delete-btn .fa-spin {
    margin-right: 5px;
}

/* 确保删除中的行内容不可选择 */
.data-table tr.deleting {
    user-select: none;
}

/* 删除按钮过渡效果 */
.batch-delete-btn {
    transition: opacity 0.3s ease;
}

/* 移动端侧边栏样式优化 */
@media (max-width: 768px) {
    .sidebar {
        background-color: #2c3e50; /* 深色背景 */
    }

    /* 侧边栏标题样式 */
    .sidebar h2 {
        color: #fff;
    }

    /* 侧边栏菜单项样式 */
    .sidebar ul li a {
        color: #fff;
        border-left: 3px solid transparent;
    }

    .sidebar ul li a:hover {
        background-color: rgba(255, 255, 255, 0.1);
        border-left-color: #1abc9c;
    }

    .sidebar ul li a.active {
        background-color: #1abc9c;
        margin-left: -3px; /* 向左偏移以覆盖白边 */
        padding-left: 23px; /* 补偿左偏移，保持内容对齐 */
        width: calc(100% + 3px); /* 确保宽度补偿偏移量 */
    }

    /* 图标颜色 */
    .sidebar ul li a i {
        color: #fff;
    }

    /* 菜单文字 */
    .sidebar ul li a span {
        color: #fff;
    }

    /* 顶部导航栏菜单图标 */
    #menu-toggle {
        color: #2c3e50; /* 深色图标 */
    }
}

/* 下载按钮样式 */
.download-btn {
    background-color: #1abc9c;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    margin-left: auto;
    transition: background-color 0.3s;
}

.download-btn:hover {
    background-color: #16a085;
}

.download-btn i {
    font-size: 14px;
}

@media (max-width: 768px) {
    .download-btn {
        padding: 6px 12px;
        font-size: 13px;
    }
}
