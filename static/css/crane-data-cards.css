/* 起重机专用数据卡片样式 - 简洁清晰 */

/* 起重机卡片容器 */
.crane-data-module {
    font-family: 'Inter', 'Segoe UI', <PERSON><PERSON>, sans-serif;
    margin: 16px 12px;
    width: 380px;
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.2),
        0 1px 2px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.15);
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

/* 起重机专用顶部发光条 */
.crane-data-module::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(251, 191, 36, 0.8) 25%, 
        rgba(245, 158, 11, 0.8) 50%, 
        rgba(217, 119, 6, 0.8) 75%, 
        transparent 100%);
    animation: craneGlow 4s ease-in-out infinite;
}

@keyframes craneGlow {
    0%, 100% { 
        opacity: 0.4; 
        transform: scaleX(0.6);
    }
    50% { 
        opacity: 1; 
        transform: scaleX(1);
    }
}

/* 悬停效果 */
.crane-data-module:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 
        0 25px 50px rgba(0, 0, 0, 0.25),
        0 10px 20px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-color: rgba(251, 191, 36, 0.4);
}

.crane-data-module:hover::before {
    animation-duration: 2s;
    opacity: 1;
    transform: scaleX(1);
}

/* 起重机模块头部 */
.crane-module-header {
    padding: 20px 24px 16px 24px;
    background: linear-gradient(135deg, 
        rgba(251, 191, 36, 0.15) 0%, 
        rgba(245, 158, 11, 0.15) 50%, 
        rgba(217, 119, 6, 0.15) 100%);
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.crane-module-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 24px;
    right: 24px;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(251, 191, 36, 0.5), transparent);
}

/* 起重机图标和文字 */
.crane-module-header span:first-child {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
}

.crane-module-header i {
    font-size: 18px;
    color: #fbbf24;
    text-shadow: 0 0 8px rgba(251, 191, 36, 0.5);
}

/* 起重机状态徽章 */
.crane-status-badge {
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: craneStatusPulse 3s ease-in-out infinite;
}

@keyframes craneStatusPulse {
    0%, 100% { 
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }
    50% { 
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3), 0 0 20px currentColor;
    }
}

.crane-status-running {
    background: linear-gradient(135deg, #10b981, #059669) !important;
    color: #ffffff;
}

.crane-status-stopped {
    background: linear-gradient(135deg, #6b7280, #4b5563) !important;
    color: #ffffff;
}

.crane-status-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706) !important;
    color: #ffffff;
}

.crane-status-error {
    background: linear-gradient(135deg, #ef4444, #dc2626) !important;
    color: #ffffff;
}

/* IP信息区域 */
.crane-ip-info {
    padding: 12px 24px;
    background: rgba(251, 191, 36, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.crane-ip-text {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    font-family: 'Courier New', monospace;
}

/* 起重机模块主体 */
.crane-module-body {
    padding: 24px;
    background: rgba(255, 255, 255, 0.02);
}

/* 起重机数据网格 - 3列布局 */
.crane-data-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-bottom: 20px;
}

/* 起重机数据项 */
.crane-data-item {
    position: relative;
    padding: 16px 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    text-align: center;
}

.crane-data-item:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(251, 191, 36, 0.3);
    transform: translateY(-2px);
}

/* 起重机数据标签 */
.crane-data-label {
    color: rgba(255, 255, 255, 0.6);
    font-size: 11px;
    font-weight: 500;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.crane-data-label::before {
    content: '';
    width: 2px;
    height: 10px;
    background: linear-gradient(180deg, #fbbf24, #f59e0b);
    border-radius: 1px;
}

/* 起重机数据值 */
.crane-data-value {
    font-size: 18px;
    font-weight: 700;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    line-height: 1.2;
}

/* 起重机数值单位 */
.crane-value-unit {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.5);
    font-weight: 400;
    margin-left: 2px;
}

/* 起重机状态指示器 */
.crane-status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 6px;
    position: relative;
    box-shadow: 0 0 10px currentColor;
    flex-shrink: 0;
}

.crane-status-indicator::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    background: currentColor;
    opacity: 0.3;
    animation: craneIndicatorPulse 2s ease-in-out infinite;
}

@keyframes craneIndicatorPulse {
    0%, 100% { 
        transform: scale(1); 
        opacity: 0.3; 
    }
    50% { 
        transform: scale(1.2); 
        opacity: 0.1; 
    }
}

.crane-indicator-normal {
    background: #10b981;
    color: #10b981;
}

.crane-indicator-warning {
    background: #f59e0b;
    color: #f59e0b;
}

.crane-indicator-danger {
    background: #ef4444;
    color: #ef4444;
}

/* 特定数据项颜色 */
.crane-data-item.frequency .crane-data-value {
    color: #60a5fa;
}

.crane-data-item.weight .crane-data-value {
    color: #f87171;
}

.crane-data-item.position .crane-data-value {
    color: #34d399;
}

/* 起重机更新时间 */
.crane-update-time {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.4);
    text-align: center;
    margin-top: 16px;
    padding-top: 12px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-style: italic;
}

.crane-update-time::before {
    content: '⚡ ';
    margin-right: 4px;
    color: #fbbf24;
}

/* 响应式优化 */
@media (max-width: 1400px) {
    .crane-data-module {
        width: 350px;
        margin: 12px 8px;
    }
    
    .crane-data-grid {
        gap: 14px;
    }
    
    .crane-data-value {
        font-size: 16px;
    }
}

@media (max-width: 1200px) {
    .crane-data-module {
        width: 320px;
        margin: 10px 6px;
    }
    
    .crane-data-grid {
        gap: 12px;
    }
    
    .crane-data-item {
        padding: 14px 10px;
    }
    
    .crane-data-value {
        font-size: 15px;
    }
}

@media (max-width: 768px) {
    .crane-data-module {
        width: 300px;
        margin: 8px 4px;
    }
    
    .crane-module-header {
        padding: 16px 20px 12px 20px;
        font-size: 15px;
    }
    
    .crane-module-body {
        padding: 20px;
    }
    
    .crane-data-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }
    
    .crane-data-item {
        padding: 12px 8px;
    }
    
    .crane-data-value {
        font-size: 14px;
    }
}

/* 加载状态 */
.crane-data-module.loading {
    opacity: 0.7;
    pointer-events: none;
}

.crane-data-module.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 24px;
    height: 24px;
    border: 2px solid transparent;
    border-top: 2px solid #fbbf24;
    border-radius: 50%;
    animation: craneLoading 1s linear infinite;
}

@keyframes craneLoading {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 离线状态 */
.crane-data-module.offline {
    opacity: 0.6;
    filter: grayscale(0.3);
}

.crane-data-module.offline .crane-status-indicator {
    background: #6b7280;
    color: #6b7280;
}

/* 错误状态 */
.crane-data-module.error {
    border-color: rgba(239, 68, 68, 0.3);
    background: rgba(239, 68, 68, 0.05);
}

.crane-data-module.error::before {
    background: linear-gradient(90deg, transparent, rgba(239, 68, 68, 0.8), transparent);
}
