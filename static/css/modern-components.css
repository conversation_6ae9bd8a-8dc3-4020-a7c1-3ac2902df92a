/* 现代化组件样式 */

/* 科技感表格样式 */
.layui-table {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.layui-table thead tr {
    background: linear-gradient(135deg, 
        rgba(59, 130, 246, 0.2) 0%, 
        rgba(147, 51, 234, 0.2) 100%);
}

.layui-table th {
    background: transparent;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    font-size: 14px;
    padding: 16px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.layui-table th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 20px;
    right: 20px;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.5), transparent);
}

.layui-table td {
    color: rgba(255, 255, 255, 0.8);
    padding: 16px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
}

.layui-table tbody tr:hover {
    background: rgba(255, 255, 255, 0.05);
}

.layui-table tbody tr:hover td {
    color: rgba(255, 255, 255, 0.95);
}

/* 现代化分页样式 */
.layui-laypage {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 24px;
    justify-content: center;
}

.layui-laypage a, .layui-laypage span {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: rgba(255, 255, 255, 0.7);
    padding: 8px 12px;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 13px;
    min-width: 36px;
    text-align: center;
}

.layui-laypage a:hover {
    background: rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.3);
    color: #ffffff;
    transform: translateY(-1px);
}

.layui-laypage .layui-laypage-curr {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    border-color: #3b82f6;
    color: #ffffff;
    font-weight: 600;
}

/* 现代化模态框样式 */
.layui-layer {
    background: rgba(30, 41, 59, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

.layui-layer-title {
    background: linear-gradient(135deg, 
        rgba(59, 130, 246, 0.2) 0%, 
        rgba(147, 51, 234, 0.2) 100%);
    color: #ffffff;
    font-weight: 600;
    font-size: 16px;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.layui-layer-content {
    background: transparent;
    color: rgba(255, 255, 255, 0.9);
    padding: 24px;
}

.layui-layer-btn {
    background: rgba(255, 255, 255, 0.05);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 16px 24px;
    text-align: right;
}

.layui-layer-btn a {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    border: none;
    border-radius: 8px;
    color: #ffffff;
    font-weight: 500;
    padding: 10px 20px;
    margin-left: 12px;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-block;
}

.layui-layer-btn a:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.layui-layer-btn .layui-layer-btn1 {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.layui-layer-btn .layui-layer-btn1:hover {
    background: rgba(255, 255, 255, 0.15);
}

/* 现代化开关样式 */
.layui-form-switch {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    width: 48px;
    height: 24px;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.layui-form-switch::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 18px;
    height: 18px;
    background: #ffffff;
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.layui-form-switch.layui-form-onswitch {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    border-color: #3b82f6;
}

.layui-form-switch.layui-form-onswitch::after {
    left: 26px;
}

/* 现代化复选框样式 */
.layui-form-checkbox {
    position: relative;
    display: inline-block;
    margin-right: 16px;
    cursor: pointer;
}

.layui-form-checkbox input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.layui-form-checkbox .layui-form-checkbox-label {
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    transition: color 0.3s ease;
}

.layui-form-checkbox .layui-form-checkbox-label::before {
    content: '';
    width: 18px;
    height: 18px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    margin-right: 8px;
    background: rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
    position: relative;
}

.layui-form-checkbox input:checked + .layui-form-checkbox-label::before {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    border-color: #3b82f6;
}

.layui-form-checkbox input:checked + .layui-form-checkbox-label::after {
    content: '✓';
    position: absolute;
    left: 5px;
    top: 1px;
    color: #ffffff;
    font-size: 12px;
    font-weight: bold;
}

.layui-form-checkbox:hover .layui-form-checkbox-label {
    color: rgba(255, 255, 255, 0.95);
}

.layui-form-checkbox:hover .layui-form-checkbox-label::before {
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.08);
}

/* 现代化单选框样式 */
.layui-form-radio {
    position: relative;
    display: inline-block;
    margin-right: 16px;
    cursor: pointer;
}

.layui-form-radio input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.layui-form-radio .layui-form-radio-label {
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    transition: color 0.3s ease;
}

.layui-form-radio .layui-form-radio-label::before {
    content: '';
    width: 18px;
    height: 18px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    margin-right: 8px;
    background: rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
    position: relative;
}

.layui-form-radio input:checked + .layui-form-radio-label::before {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.2);
}

.layui-form-radio input:checked + .layui-form-radio-label::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 6px;
    width: 6px;
    height: 6px;
    background: #3b82f6;
    border-radius: 50%;
}

/* 现代化进度条样式 */
.layui-progress {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    height: 8px;
    overflow: hidden;
    position: relative;
}

.layui-progress-bar {
    background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 50%, #ec4899 100%);
    height: 100%;
    border-radius: 10px;
    transition: width 0.6s ease;
    position: relative;
    overflow: hidden;
}

.layui-progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progressShine 2s ease-in-out infinite;
}

@keyframes progressShine {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* 响应式优化 */
@media (max-width: 768px) {
    .layui-table th,
    .layui-table td {
        padding: 12px 16px;
        font-size: 13px;
    }
    
    .layui-layer {
        margin: 20px;
        width: calc(100% - 40px) !important;
    }
    
    .layui-layer-title {
        padding: 16px 20px;
        font-size: 15px;
    }
    
    .layui-layer-content {
        padding: 20px;
    }
}
