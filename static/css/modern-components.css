/* ===== 现代化组件库 ===== */

/* CSS变量定义 */
:root {
    /* 主色调 */
    --primary: #667eea;
    --primary-light: #8fa4f3;
    --primary-dark: #4c63d2;
    --secondary: #764ba2;
    --accent: #f093fb;
    
    /* 功能色 */
    --success: #00d4aa;
    --warning: #ffb74d;
    --error: #ff5252;
    --info: #4facfe;
    
    /* 中性色 */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* 渐变 */
    --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    --gradient-success: linear-gradient(135deg, #00d4aa 0%, #00b894 100%);
    --gradient-warning: linear-gradient(135deg, #ffb74d 0%, #ff9500 100%);
    --gradient-error: linear-gradient(135deg, #ff5252 0%, #d32f2f 100%);
    
    /* 阴影 */
    --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.2);
    
    /* 圆角 */
    --radius-xs: 4px;
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    --radius-full: 9999px;
    
    /* 过渡 */
    --transition-fast: all 0.15s ease;
    --transition-normal: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== 现代化按钮组件 ===== */
.btn-modern {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-normal);
    text-decoration: none;
    position: relative;
    overflow: hidden;
    user-select: none;
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-modern:hover::before {
    left: 100%;
}

.btn-modern:hover {
    transform: translateY(-2px);
}

.btn-modern:active {
    transform: translateY(0);
}

/* 按钮变体 */
.btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    box-shadow: var(--shadow-lg);
}

.btn-success {
    background: var(--gradient-success);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-warning {
    background: var(--gradient-warning);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-error {
    background: var(--gradient-error);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-outline {
    background: transparent;
    color: var(--primary);
    border: 2px solid var(--primary);
}

.btn-outline:hover {
    background: var(--primary);
    color: var(--white);
}

.btn-ghost {
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary);
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.btn-ghost:hover {
    background: rgba(102, 126, 234, 0.2);
}

/* 按钮尺寸 */
.btn-sm {
    padding: 8px 16px;
    font-size: 12px;
}

.btn-lg {
    padding: 16px 32px;
    font-size: 16px;
}

/* ===== 现代化输入框组件 ===== */
.input-modern {
    width: 100%;
    padding: 12px 16px;
    font-size: 14px;
    line-height: 1.5;
    color: var(--gray-700);
    background: var(--white);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
    outline: none;
}

.input-modern:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
}

.input-modern::placeholder {
    color: var(--gray-400);
}

/* 输入框组 */
.input-group {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.input-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--gray-700);
}

.input-error {
    border-color: var(--error);
}

.input-error:focus {
    border-color: var(--error);
    box-shadow: 0 0 0 4px rgba(255, 82, 82, 0.1);
}

.input-success {
    border-color: var(--success);
}

.input-success:focus {
    border-color: var(--success);
    box-shadow: 0 0 0 4px rgba(0, 212, 170, 0.1);
}

/* ===== 现代化卡片组件 ===== */
.card-modern {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: var(--transition-normal);
    position: relative;
}

.card-modern:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.card-header {
    padding: 24px 24px 16px;
    border-bottom: 1px solid var(--gray-200);
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0 0 8px 0;
}

.card-subtitle {
    font-size: 14px;
    color: var(--gray-500);
    margin: 0;
}

.card-body {
    padding: 24px;
}

.card-footer {
    padding: 16px 24px;
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
}

/* ===== 现代化徽章组件 ===== */
.badge-modern {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    font-size: 12px;
    font-weight: 500;
    line-height: 1;
    border-radius: var(--radius-full);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary {
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary);
}

.badge-success {
    background: rgba(0, 212, 170, 0.1);
    color: var(--success);
}

.badge-warning {
    background: rgba(255, 183, 77, 0.1);
    color: var(--warning);
}

.badge-error {
    background: rgba(255, 82, 82, 0.1);
    color: var(--error);
}

/* ===== 现代化加载动画 ===== */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--gray-200);
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-dots {
    display: inline-flex;
    gap: 4px;
}

.loading-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--primary);
    animation: bounce 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

/* ===== 响应式工具类 ===== */
@media (max-width: 768px) {
    .btn-modern {
        padding: 10px 20px;
        font-size: 13px;
    }
    
    .card-modern {
        margin: 0 16px;
    }
    
    .card-header,
    .card-body {
        padding: 20px;
    }
}
