.tab-input {
    padding-right: 10px;
}

#left-nav {
    width: 100%;
    height: 100%
}

.input-label {
    padding-left: 1pt;
    padding-right: 0;
    text-align: center;
}

.tab-child {
    padding-right: 0;
    padding-left: 0;
}

.search-input {
    width: 60%;
}

.layui-select-title {
    /*padding-right: 10px;*/
    padding-right: 0;
}

.layui-input-inline {
    width: 60% !important;
}

.tab-layui-form-item {
    margin-bottom: 0;
}
.data-tab {
    padding: 0;
    padding-top: 10px
}

.search-label {
    width: 65px !important;
}

.info-list-btn{
    width: 100px;
    height: 100px;
    padding: 0;
}


/*数据面板 - 现代化科技风格*/
.compact-data-module{
    font-family: 'Inter', 'Segoe UI', Arial, sans-serif;
    margin-left: 20px;
    margin-top: 20px;
    width: 280px;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 16px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.12),
        0 2px 8px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    font-size: 13px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.compact-data-module::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.5), transparent);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

.compact-data-module:hover {
    transform: translateY(-4px);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.15),
        0 8px 16px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}
.module-header{
    padding: 16px 20px;
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.9) 0%,
        rgba(147, 51, 234, 0.9) 50%,
        rgba(236, 72, 153, 0.9) 100%);
    color: white;
    font-size: 15px;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.module-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: headerShine 3s ease-in-out infinite;
}

@keyframes headerShine {
    0% { left: -100%; }
    100% { left: 100%; }
}
.module-body{
    padding: 20px 20px 16px 20px;
    background: rgba(255, 255, 255, 0.02);
}
.data-grid{
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
}
.data-label{
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
    margin-bottom: 4px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}
.data-value{
    font-size: 16px;
    font-weight: 700;
    color: #ffffff;
    display: flex;
    align-items: center;
    position: relative;
}

.data-value::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 20px;
    background: linear-gradient(180deg, #3b82f6, #8b5cf6);
    border-radius: 2px;
}

.value-unit{
    font-size: 11px;
    color: rgba(255, 255, 255, 0.5);
    margin-left: 4px;
    font-weight: 400;
}
.status-indicator{
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
    position: relative;
    box-shadow: 0 0 8px currentColor;
}

.status-indicator::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    background: currentColor;
    opacity: 0.3;
    animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
    0%, 100% { transform: scale(1); opacity: 0.3; }
    50% { transform: scale(1.2); opacity: 0.1; }
}

.status-normal{
    background: #10b981;
    color: #10b981;
}
.status-warning{
    background: #f59e0b;
    color: #f59e0b;
}
.status-danger{
    background: #ef4444;
    color: #ef4444;
}
.location-info{
    grid-column: span 2;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 12px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 13px;
}
.updata-time{
    font-size: 11px;
    color: rgba(255, 255, 255, 0.5);
    text-align: right;
    margin-top: 8px;
    font-style: italic;
}
.card-ip{
    font-size: 10px;
    color: rgba(255, 255, 255, 0.4);
    text-align: left;
    margin-top: 4px;
    padding-left: 20px;
    padding-right: 20px;
    font-family: 'Courier New', monospace;
    background: rgba(255, 255, 255, 0.05);
    padding: 4px 12px;
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/*右侧tab页面滚动*/
.scrollable-tab{
    display: flex;
    flex-direction: column;
    height: 100vh;
    margin-top: 0;
}


.layui-tab-content {
    flex: 1;
    overflow: auto;
    padding: 15px;
}

/*.data-tab {*/
/*    flex: 1;*/
/*    overflow: auto;*/
/*    padding: 15px;*/
/*}*/

#card_page_index {
    text-align: center;
    margin-top: 15px;
}

.card-page {
    height: 426px;
}
#crane_card_page_index{
    text-align: center;
    margin-top: 15px;
}
.crane-card-page{
    height: 426px;
}
body {
    /*overflow: hidden;*/
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
    min-height: 100vh;
}

/* 现代化Tab标签页样式 */
.layui-tab-brief {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.layui-tab-title {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.1) 0%,
        rgba(147, 51, 234, 0.1) 50%,
        rgba(236, 72, 153, 0.1) 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 0;
    margin: 0;
    position: relative;
}

.layui-tab-title::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.5), transparent);
}

.layui-tab-title li {
    background: transparent;
    border: none;
    margin: 0;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.layui-tab-title li a {
    color: rgba(255, 255, 255, 0.7);
    padding: 20px 32px;
    font-weight: 500;
    font-size: 15px;
    text-decoration: none;
    display: block;
    position: relative;
    transition: all 0.3s ease;
}

.layui-tab-title li:hover {
    background: rgba(255, 255, 255, 0.05);
}

.layui-tab-title li:hover a {
    color: rgba(255, 255, 255, 0.9);
}

.layui-tab-title li.layui-this {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.2) 0%,
        rgba(147, 51, 234, 0.2) 100%);
    border-bottom: 3px solid #3b82f6;
}

.layui-tab-title li.layui-this a {
    color: #ffffff;
    font-weight: 600;
}

.layui-tab-title li.layui-this::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
    animation: tabGlow 2s ease-in-out infinite;
}

@keyframes tabGlow {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

.layui-tab-content {
    background: rgba(255, 255, 255, 0.02);
    padding: 24px;
    min-height: 600px;
}

/* 现代化顶部导航栏样式 */
.modern-top-nav {
    background: linear-gradient(135deg,
        rgba(15, 23, 42, 0.95) 0%,
        rgba(30, 41, 59, 0.95) 50%,
        rgba(51, 65, 85, 0.95) 100%);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 1000;
}

.modern-top-nav::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.5), transparent);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32px;
    height: 70px;
    max-width: 1400px;
    margin: 0 auto;
}

.nav-brand {
    display: flex;
    align-items: center;
}

.brand-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    transition: all 0.3s ease;
}

.brand-link:hover {
    transform: translateY(-1px);
}

.brand-logo {
    width: auto;
    height: 45px;
    object-fit: contain;
    margin-right: 16px;
    filter: brightness(1.1);
}

.brand-text {
    color: #ffffff;
    font-size: 20px;
    font-weight: 600;
    letter-spacing: 0.5px;
    background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 24px;
}

.nav-user {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.nav-user:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 16px;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.user-name {
    color: #ffffff;
    font-size: 14px;
    font-weight: 600;
    line-height: 1.2;
}

.user-role {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
    line-height: 1.2;
}

.user-dropdown {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
    transition: all 0.3s ease;
}

.nav-user:hover .user-dropdown {
    color: #ffffff;
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 8px;
    background: rgba(30, 41, 59, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    min-width: 180px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1001;
}

.nav-user:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.2s ease;
    border-radius: 8px;
    margin: 4px;
}

.dropdown-item:hover {
    background: rgba(59, 130, 246, 0.2);
    color: #ffffff;
}

.dropdown-item i {
    font-size: 16px;
    width: 16px;
    text-align: center;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 16px;
        height: 60px;
    }

    .brand-logo {
        height: 35px;
        margin-right: 12px;
    }

    .brand-text {
        font-size: 16px;
        display: none;
    }

    .user-info {
        display: none;
    }

    .nav-user {
        padding: 6px 12px;
    }

    .dropdown-menu {
        right: -20px;
        min-width: 160px;
    }
}

