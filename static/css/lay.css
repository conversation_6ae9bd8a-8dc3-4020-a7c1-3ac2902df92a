/* ===== 现代化设计系统 ===== */
:root {
    --primary-color: #667eea;
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-color: #f093fb;
    --accent-color: #4facfe;
    --success-color: #00d4aa;
    --warning-color: #ffb74d;
    --error-color: #ff5252;
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --text-light: #bdc3c7;
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-card: #ffffff;
    --border-color: #e2e8f0;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.2);
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 全局样式重置和优化 */
* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* 基础组件样式 */
.tab-input {
    padding-right: 12px;
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.tab-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

#left-nav {
    width: 100%;
    height: 100%;
    background: var(--bg-card);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
}

.input-label {
    padding-left: 8px;
    padding-right: 8px;
    text-align: center;
    font-weight: 500;
    color: var(--text-secondary);
}

.tab-child {
    padding: 0 16px;
}

.search-input {
    width: 70%;
    border-radius: var(--radius-md);
    border: 2px solid var(--border-color);
    padding: 12px 16px;
    font-size: 14px;
    transition: var(--transition);
    background: var(--bg-card);
}

.search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    outline: none;
}

.layui-select-title {
    padding-right: 12px;
    border-radius: var(--radius-sm);
}

.layui-input-inline {
    width: 70% !important;
}

.tab-layui-form-item {
    margin-bottom: 16px;
}

.data-tab {
    padding: 20px;
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    margin: 16px;
}

.search-label {
    width: 80px !important;
    font-weight: 500;
    color: var(--text-primary);
}

.info-list-btn {
    width: 120px;
    height: 120px;
    padding: 16px;
    border-radius: var(--radius-lg);
    background: var(--primary-gradient);
    color: white;
    border: none;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    cursor: pointer;
    font-weight: 500;
}

.info-list-btn:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}


/* ===== 现代化数据面板 ===== */
.compact-data-module {
    font-family: inherit;
    margin: 16px;
    width: 320px;
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    font-size: 14px;
    border: 1px solid var(--border-color);
    transition: var(--transition);
    position: relative;
}

.compact-data-module:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.compact-data-module::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.module-header {
    padding: 20px 24px 16px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    position: relative;
}

.module-header::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 24px;
    right: 24px;
    height: 2px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

.module-body {
    padding: 24px;
}

.data-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.data-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.data-label {
    color: var(--text-secondary);
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.data-value {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.value-unit {
    font-size: 12px;
    color: var(--text-light);
    font-weight: 400;
    margin-left: 4px;
}

.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.8);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.status-normal {
    background: var(--success-color);
    box-shadow: 0 0 0 2px rgba(0, 212, 170, 0.2);
}

.status-warning {
    background: var(--warning-color);
    box-shadow: 0 0 0 2px rgba(255, 183, 77, 0.2);
}

.status-danger {
    background: var(--error-color);
    box-shadow: 0 0 0 2px rgba(255, 82, 82, 0.2);
}

.location-info {
    grid-column: span 2;
    padding: 16px 0;
    border-bottom: 2px dashed var(--border-color);
    margin-bottom: 16px;
    font-weight: 500;
    color: var(--text-primary);
}

.updata-time {
    font-size: 11px;
    color: var(--text-light);
    text-align: right;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid var(--border-color);
}

.card-ip {
    font-size: 10px;
    color: var(--text-light);
    text-align: center;
    margin-top: 8px;
    padding: 8px 16px;
    background: rgba(102, 126, 234, 0.05);
    border-radius: var(--radius-sm);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* ===== 现代化Tab系统 ===== */
.scrollable-tab {
    display: flex;
    flex-direction: column;
    height: 100vh;
    margin-top: 0;
    background: var(--bg-secondary);
}

/* Tab标题栏优化 */
.layui-tab-title {
    background: var(--bg-card);
    border-bottom: 2px solid var(--border-color);
    padding: 0 24px;
    box-shadow: var(--shadow-sm);
    position: relative;
}

.layui-tab-title li {
    position: relative;
    margin-right: 8px;
    border-radius: var(--radius-md) var(--radius-md) 0 0;
    transition: var(--transition);
    overflow: hidden;
}

.layui-tab-title li a {
    padding: 16px 24px;
    font-weight: 500;
    color: var(--text-secondary);
    text-decoration: none;
    display: block;
    transition: var(--transition);
    position: relative;
}

.layui-tab-title li:hover a {
    color: var(--primary-color);
    background: rgba(102, 126, 234, 0.05);
}

.layui-tab-title li.layui-this {
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.layui-tab-title li.layui-this a {
    color: white;
    font-weight: 600;
}

.layui-tab-title li.layui-this::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

/* Tab内容区域 */
.layui-tab-content {
    flex: 1;
    overflow: auto;
    padding: 24px;
    background: var(--bg-secondary);
}

.layui-tab-item {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: 24px;
    margin-bottom: 24px;
    border: 1px solid var(--border-color);
}

/* 分页组件现代化 */
#card_page_index,
#crane_card_page_index {
    text-align: center;
    margin: 24px 0;
    padding: 16px;
    background: var(--bg-card);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
}

.card-page,
.crane-card-page {
    min-height: 500px;
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    padding: 24px;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

/* 滚动条美化 */
.layui-tab-content::-webkit-scrollbar {
    width: 8px;
}

.layui-tab-content::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 4px;
}

.layui-tab-content::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
    transition: var(--transition);
}

.layui-tab-content::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
    opacity: 0.8;
}

body {
    overflow-x: hidden;
    background: var(--bg-secondary);
}

/* ===== 现代化表单组件 ===== */
.layui-form-item {
    margin-bottom: 24px;
}

.layui-form-label {
    font-weight: 500;
    color: var(--text-primary);
    padding: 12px 16px;
    background: rgba(102, 126, 234, 0.05);
    border-radius: var(--radius-sm) 0 0 var(--radius-sm);
    border: 1px solid var(--border-color);
    border-right: none;
}

.layui-input,
.layui-select,
.layui-textarea {
    border: 1px solid var(--border-color);
    border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
    padding: 12px 16px;
    font-size: 14px;
    transition: var(--transition);
    background: var(--bg-card);
}

.layui-input:focus,
.layui-select:focus,
.layui-textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

/* 按钮现代化 */
.layui-btn {
    border-radius: var(--radius-md);
    padding: 12px 24px;
    font-weight: 500;
    font-size: 14px;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.layui-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.layui-btn:hover::before {
    left: 100%;
}

.layui-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.layui-btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--shadow-sm);
}

.layui-btn-primary:hover {
    box-shadow: var(--shadow-lg);
}

/* 表格现代化 */
.layui-table {
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

.layui-table th {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    color: var(--text-primary);
    font-weight: 600;
    padding: 16px;
    border-bottom: 2px solid var(--border-color);
}

.layui-table td {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
}

.layui-table tr:hover td {
    background: rgba(102, 126, 234, 0.05);
}

/* 加载动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.compact-data-module {
    animation: fadeInUp 0.6s ease-out;
}

.layui-tab-item {
    animation: slideInRight 0.4s ease-out;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .compact-data-module {
        width: 280px;
        margin: 12px;
    }

    .data-tab {
        padding: 16px;
        margin: 12px;
    }
}

@media (max-width: 768px) {
    .compact-data-module {
        width: calc(100% - 24px);
        margin: 12px;
    }

    .layui-tab-title {
        padding: 0 16px;
        overflow-x: auto;
        white-space: nowrap;
    }

    .layui-tab-title li {
        display: inline-block;
        margin-right: 4px;
    }

    .layui-tab-title li a {
        padding: 12px 16px;
        font-size: 13px;
    }

    .layui-tab-content {
        padding: 16px;
    }

    .layui-tab-item {
        padding: 16px;
    }

    .data-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .module-body {
        padding: 16px;
    }
}

@media (max-width: 480px) {
    .layui-form-label {
        padding: 8px 12px;
        font-size: 13px;
    }

    .layui-input,
    .layui-select {
        padding: 8px 12px;
        font-size: 13px;
    }

    .layui-btn {
        padding: 10px 16px;
        font-size: 13px;
    }
}

/* ===== 新增页面结构样式 ===== */
.main-wrapper {
    min-height: 100vh;
    background: var(--bg-secondary);
}

.content-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Tab内容头部 */
.tab-content-header {
    background: var(--bg-card);
    padding: 24px;
    border-radius: var(--radius-lg);
    margin-bottom: 24px;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.tab-content-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.tab-content-header h2 {
    color: var(--text-primary);
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.tab-content-header h2 i {
    color: var(--primary-color);
    font-size: 20px;
}

.tab-content-header p {
    color: var(--text-secondary);
    font-size: 14px;
    margin: 0;
    line-height: 1.5;
}

/* iframe占位符样式 */
.iframe-container {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.iframe-placeholder {
    text-align: center;
    color: var(--text-secondary);
    padding: 40px;
}

.iframe-placeholder i {
    color: var(--primary-color);
    margin-bottom: 20px;
    opacity: 0.7;
}

.iframe-placeholder h3 {
    color: var(--text-primary);
    font-size: 20px;
    font-weight: 500;
    margin: 0 0 12px 0;
}

.iframe-placeholder p {
    font-size: 14px;
    margin: 0;
    line-height: 1.5;
}

/* Tab标题图标样式 */
.layui-tab-title li a {
    display: flex;
    align-items: center;
    gap: 8px;
}

.layui-tab-title li a i {
    font-size: 16px;
    transition: var(--transition);
}

.layui-tab-title li:hover a i {
    transform: scale(1.1);
}

/* 数据网格增强 */
.data-grid-enhanced {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    margin: 24px 0;
}

/* 状态卡片 */
.status-card {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    padding: 24px;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.status-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.status-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.status-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.status-card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-card-value {
    font-size: 28px;
    font-weight: 700;
    color: var(--primary-color);
    margin: 8px 0;
}

.status-card-description {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.5;
}

/* 响应式优化 */
@media (max-width: 1024px) {
    .content-container {
        padding: 0 16px;
    }

    .tab-content-header {
        padding: 20px;
    }

    .tab-content-header h2 {
        font-size: 20px;
    }
}

@media (max-width: 768px) {
    .tab-content-header h2 {
        font-size: 18px;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .data-grid-enhanced {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .status-card {
        padding: 20px;
    }
}

