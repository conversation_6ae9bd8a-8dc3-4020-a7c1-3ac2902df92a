<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>智能洞库管理系统 - 数字化综合管理平台</title>
    <meta name="description" content="智能洞库管理系统，提供设备监控、数据分析、智能管理等功能">

    <!-- 预加载关键资源 -->
    <link rel="preload" href="/static/css/layui.css" as="style">
    <link rel="preload" href="/static/css/lay.css" as="style">

    <!-- 样式表 -->
    <link href="/static/css/layui.css" rel="stylesheet" media="all">
    <link href="/static/css/lay.css" rel="stylesheet" media="all">
    <link href="/static/css/modern-components.css" rel="stylesheet" media="all">

    <!-- 图标字体 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- JavaScript -->
    <script src="/static/js/layui.js"></script>
    <script src="/static/js/jquery.js"></script>
    <script src="/static/js/home.js"></script>

    <!-- 页面加载动画样式 -->
    <style>
        .page-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease-out;
        }

        .loader-content {
            text-align: center;
            color: white;
        }

        .loader-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loader-text {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 10px;
        }

        .loader-subtext {
            font-size: 14px;
            opacity: 0.8;
        }
    </style>
</head>


<body>
    <!-- 页面加载动画 -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-content">
            <div class="loader-spinner"></div>
            <div class="loader-text">智能洞库管理系统</div>
            <div class="loader-subtext">正在加载数据...</div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-wrapper" style="opacity: 0; transition: opacity 0.5s ease-in;">
        {#顶部导航栏#}
        {% include 'top_nav.html' %}

        {#主要内容区域#}
        <div class="content-container">
            <div class="layui-tab layui-tab-brief scrollable-tab" lay-filter="top-tab">
                <ul class="layui-tab-title tab_ch_flag" id="{{ tab_ch }}">
                    <li class="layui-this" lay-id="icon-TH-l">
                        <a href="javascript:;">
                            <i class="fas fa-thermometer-half"></i>
                            <span>空调监控</span>
                        </a>
                    </li>
                    <li lay-id="icon-door-l">
                        <a href="javascript:;">
                            <i class="fas fa-door-open"></i>
                            <span>门禁检测</span>
                        </a>
                    </li>
                    <li class="inventory top-tab-btn" lay-id="icon-warehouse-l">
                        <a href="javascript:;">
                            <i class="fas fa-warehouse"></i>
                            <span>库存管理</span>
                        </a>
                    </li>
                    <li lay-id="icon-crane-l">
                        <a href="javascript:;">
                            <i class="fas fa-cog"></i>
                            <span>起重机管理</span>
                        </a>
                    </li>
                </ul>
                <div class="layui-tab-content">
                    {#空调监控页面#}
                    <div class="layui-tab-item layui-show">
                        <div class="tab-content-header">
                            <h2><i class="fas fa-thermometer-half"></i> 空调监控系统</h2>
                            <p>实时监控环境温湿度及空调设备状态</p>
                        </div>
                        {% include 'airconditioner_right_page.html' %}
                    </div>

                    <div class="layui-tab-item">
                        <div class="tab-content-header">
                            <h2><i class="fas fa-door-open"></i> 门禁检测系统</h2>
                            <p>智能门禁管理与安全监控</p>
                        </div>
                        {% include 'ctrdoor.html' %}
                    </div>

                    <div class="layui-tab-item iframe_item">
                        <div class="tab-content-header">
                            <h2><i class="fas fa-warehouse"></i> 库存管理系统</h2>
                            <p>智能化库存管理与数据分析</p>
                        </div>
                        <div class="iframe-container">
                            <div class="iframe-placeholder">
                                <i class="fas fa-cogs fa-3x"></i>
                                <h3>系统集成中</h3>
                                <p>库存管理模块正在开发完善中...</p>
                            </div>
                        </div>
                    </div>

                    <div class="layui-tab-item">
                        <div class="tab-content-header">
                            <h2><i class="fas fa-cog"></i> 起重机管理系统</h2>
                            <p>设备状态监控与维护管理</p>
                        </div>
                        {% include 'crane_table.html' %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    {#模态框和弹窗#}
    {% include 'device_table.html' %}
    {% include 'add_device.html' %}
    {% include 'edit_device.html' %}
    {% include 'history.html' %}

    <!-- 页面加载完成脚本 -->
    <script>
        // 页面加载完成后隐藏加载动画
        window.addEventListener('load', function() {
            setTimeout(function() {
                const loader = document.getElementById('pageLoader');
                const mainWrapper = document.querySelector('.main-wrapper');

                loader.style.opacity = '0';
                mainWrapper.style.opacity = '1';

                setTimeout(function() {
                    loader.style.display = 'none';
                }, 500);
            }, 800); // 延迟800ms显示完整加载效果
        });

        // 添加页面切换动画
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有tab添加切换动画
            const tabItems = document.querySelectorAll('.layui-tab-item');
            tabItems.forEach(item => {
                item.style.transition = 'all 0.3s ease-in-out';
            });
        });
    </script>
</body>




